package com.my_mueller.simlogparser;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

class SqlGeneratorServiceTest {

    @Test
    void testGenerateSqlFileWithConfiguredStatements(@TempDir Path tempDir) throws IOException {
        // Create configuration with custom SQL statements
        SqlConfiguration config = new SqlConfiguration();
        config.setStatements(List.of(
            "UPDATE table1 SET status = 'SUSPENDED' WHERE sim = '{sim_number}';",
            "INSERT INTO audit (sim, action) VALUES ('{sim_number}', 'SUSPEND');",
            "DELETE FROM active_sims WHERE sim = '{sim_number}';"
        ));
        
        SqlGeneratorService service = new SqlGeneratorService(config);
        Set<String> suspendedSims = Set.of("123456789012345", "123456789012344");
        
        // Generate SQL file
        service.generateSqlFile(suspendedSims, tempDir);
        
        // Find the generated SQL file
        List<Path> sqlFiles = Files.list(tempDir)
            .filter(path -> path.getFileName().toString().startsWith("suspended_sims_"))
            .filter(path -> path.getFileName().toString().endsWith(".sql"))
            .toList();
        
        assertEquals(1, sqlFiles.size(), "Should generate exactly one SQL file");
        
        // Read and verify the SQL file content
        String content = Files.readString(sqlFiles.get(0));
        
        // Verify header information
        assertTrue(content.contains("Total suspended SIMs: 2"));
        assertTrue(content.contains("Total SQL statements per SIM: 3"));
        
        // Verify configured statements are listed
        assertTrue(content.contains("UPDATE table1 SET status = 'SUSPENDED' WHERE sim = '{sim_number}';"));
        assertTrue(content.contains("INSERT INTO audit (sim, action) VALUES ('{sim_number}', 'SUSPEND');"));
        assertTrue(content.contains("DELETE FROM active_sims WHERE sim = '{sim_number}';"));
        
        // Verify actual statements with SIM numbers replaced
        assertTrue(content.contains("UPDATE table1 SET status = 'SUSPENDED' WHERE sim = '123456789012345';"));
        assertTrue(content.contains("INSERT INTO audit (sim, action) VALUES ('123456789012345', 'SUSPEND');"));
        assertTrue(content.contains("DELETE FROM active_sims WHERE sim = '123456789012345';"));
        
        assertTrue(content.contains("UPDATE table1 SET status = 'SUSPENDED' WHERE sim = '123456789012344';"));
        assertTrue(content.contains("INSERT INTO audit (sim, action) VALUES ('123456789012344', 'SUSPEND');"));
        assertTrue(content.contains("DELETE FROM active_sims WHERE sim = '123456789012344';"));
        
        // Verify total statements count
        assertTrue(content.contains("Total statements generated: 6"));
    }

    @Test
    void testGenerateSqlFileWithEmptyConfiguration(@TempDir Path tempDir) throws IOException {
        // Create configuration with no SQL statements
        SqlConfiguration config = new SqlConfiguration();
        config.setStatements(List.of()); // Empty list
        
        SqlGeneratorService service = new SqlGeneratorService(config);
        Set<String> suspendedSims = Set.of("123456789012345");
        
        // Generate SQL file
        service.generateSqlFile(suspendedSims, tempDir);
        
        // Find the generated SQL file
        List<Path> sqlFiles = Files.list(tempDir)
            .filter(path -> path.getFileName().toString().startsWith("suspended_sims_"))
            .filter(path -> path.getFileName().toString().endsWith(".sql"))
            .toList();
        
        assertEquals(1, sqlFiles.size(), "Should generate exactly one SQL file");
        
        // Read and verify the SQL file content uses default statements
        String content = Files.readString(sqlFiles.get(0));
        
        // Should use default statements
        assertTrue(content.contains("UPDATE sim_cards SET status = 'SUSPENDED'"));
        assertTrue(content.contains("INSERT INTO sim_audit_log"));
        assertTrue(content.contains("Total statements generated: 2")); // 2 default statements
    }

    @Test
    void testGenerateSqlFileWithEmptySimSet(@TempDir Path tempDir) throws IOException {
        SqlConfiguration config = new SqlConfiguration();
        config.setStatements(List.of("UPDATE test SET status = 'SUSPENDED' WHERE sim = '{sim_number}';"));
        
        SqlGeneratorService service = new SqlGeneratorService(config);
        Set<String> suspendedSims = Set.of(); // Empty set
        
        // Generate SQL file
        service.generateSqlFile(suspendedSims, tempDir);
        
        // Should not generate any files
        List<Path> sqlFiles = Files.list(tempDir)
            .filter(path -> path.getFileName().toString().startsWith("suspended_sims_"))
            .toList();
        
        assertEquals(0, sqlFiles.size(), "Should not generate any files for empty SIM set");
    }
}
