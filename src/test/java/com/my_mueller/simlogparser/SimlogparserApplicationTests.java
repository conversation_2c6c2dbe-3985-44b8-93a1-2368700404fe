package com.my_mueller.simlogparser;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE)
class SimlogparserApplicationTests {

	@MockBean
	private SimLogParserCommandLineRunner commandLineRunner;

	@Test
	void contextLoads() {
		// This test verifies that the Spring context can be loaded
		// We mock the CommandLineRunner to prevent it from executing
	}

}
