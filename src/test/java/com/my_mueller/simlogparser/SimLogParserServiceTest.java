package com.my_mueller.simlogparser;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

class SimLogParserServiceTest {

    private final SimLogParserService parserService = new SimLogParserService();

    @Test
    void testParseLogFile_WithSuspendAndReactivate(@TempDir Path tempDir) throws IOException {
        // Create test log file
        Path logFile = tempDir.resolve("test.log");
        List<String> logLines = List.of(
            "2025-08-01 10:10:10.123 Moved sim 123456789012345 state from ACTIVE_LIVE to ACTIVE_SUSPEND successfull.",
            "2025-08-01 10:11:10.123 Moved sim 123456789012344 state from ACTIVE_LIVE to ACTIVE_SUSPEND successfull.",
            "2025-08-01 11:10:10.123 Moved sim 123456789012345 state from ACTIVE_SUSPEND to ACTIVE_LIVE successfull."
        );
        Files.write(logFile, logLines);

        // Parse the file
        Set<String> suspendedSims = parserService.parseLogFile(logFile);

        // Verify results
        assertEquals(1, suspendedSims.size());
        assertTrue(suspendedSims.contains("123456789012344"));
        assertFalse(suspendedSims.contains("123456789012345"));
    }

    @Test
    void testParseLogFile_OnlySuspensions(@TempDir Path tempDir) throws IOException {
        // Create test log file with only suspensions
        Path logFile = tempDir.resolve("test.log");
        List<String> logLines = List.of(
            "2025-08-01 10:10:10.123 Moved sim 123456789012345 state from ACTIVE_LIVE to ACTIVE_SUSPEND successfull.",
            "2025-08-01 10:11:10.123 Moved sim 123456789012344 state from ACTIVE_LIVE to ACTIVE_SUSPEND successfull.",
            "2025-08-01 10:12:10.123 Moved sim 123456789012343 state from ACTIVE_LIVE to ACTIVE_SUSPEND successfull."
        );
        Files.write(logFile, logLines);

        // Parse the file
        Set<String> suspendedSims = parserService.parseLogFile(logFile);

        // Verify results
        assertEquals(3, suspendedSims.size());
        assertTrue(suspendedSims.contains("123456789012345"));
        assertTrue(suspendedSims.contains("123456789012344"));
        assertTrue(suspendedSims.contains("123456789012343"));
    }

    @Test
    void testParseLogFile_EmptyFile(@TempDir Path tempDir) throws IOException {
        // Create empty test log file
        Path logFile = tempDir.resolve("empty.log");
        Files.write(logFile, List.of());

        // Parse the file
        Set<String> suspendedSims = parserService.parseLogFile(logFile);

        // Verify results
        assertTrue(suspendedSims.isEmpty());
    }

    @Test
    void testParseLogFile_InvalidSimNumbers(@TempDir Path tempDir) throws IOException {
        // Create test log file with invalid SIM numbers
        Path logFile = tempDir.resolve("test.log");
        List<String> logLines = List.of(
            "2025-08-01 10:10:10.123 Moved sim 12345678901234 state from ACTIVE_LIVE to ACTIVE_SUSPEND successfull.", // 14 digits
            "2025-08-01 10:11:10.123 Moved sim 1234567890123456 state from ACTIVE_LIVE to ACTIVE_SUSPEND successfull.", // 16 digits
            "2025-08-01 10:12:10.123 Moved sim abc456789012345 state from ACTIVE_LIVE to ACTIVE_SUSPEND successfull." // contains letters
        );
        Files.write(logFile, logLines);

        // Parse the file
        Set<String> suspendedSims = parserService.parseLogFile(logFile);

        // Verify results - should be empty since no valid SIM numbers
        assertTrue(suspendedSims.isEmpty());
    }
}
