spring.application.name=simlogparser

# SQL Generation Configuration - List of SQL statements to execute for each suspended SIM
# Use {sim_number} as placeholder for the actual SIM number
sql.statements[0]=UPDATE sim_cards SET status = 'SUSPENDED', last_updated = CURRENT_TIMESTAMP WHERE sim_number = '{sim_number}';
sql.statements[1]=INSERT INTO sim_audit_log (sim_number, action, timestamp, reason) VALUES ('{sim_number}', 'SUSPEND', CURRENT_TIMESTAMP, 'Automated suspension from log parser');
sql.statements[2]=UPDATE sim_billing SET billing_status = 'SUSPENDED' WHERE sim_number = '{sim_number}';

# You can add more SQL statements by incrementing the index:
# sql.statements[3]=DELETE FROM active_sessions WHERE sim_number = '{sim_number}';
# sql.statements[4]=INSERT INTO notification_queue (sim_number, message) VALUES ('{sim_number}', 'S<PERSON> suspended automatically');
