package com.my_mueller.simlogparser;

import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.HashSet;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class SimLogParserService {

    // Pattern to match SIM numbers (15 digits)
    private static final Pattern SIM_PATTERN = Pattern.compile("\\b(\\d{15})\\b");
    
    // Pattern to match state transitions
    private static final Pattern ACTIVE_LIVE_TO_SUSPEND_PATTERN = 
        Pattern.compile("ACTIVE_LIVE to ACTIVE_SUSPEND");
    
    private static final Pattern ACTIVE_SUSPEND_TO_LIVE_PATTERN = 
        Pattern.compile("ACTIVE_SUSPEND to ACTIVE_LIVE");

    /**
     * Parses the log file and returns a set of SIM numbers that are in suspended state
     * 
     * @param logFilePath Path to the log file to parse
     * @return Set of SIM numbers that are currently suspended
     * @throws IOException if there's an error reading the file
     */
    public Set<String> parseLogFile(final Path logFilePath) throws IOException {
        final Set<String> suspendedSims = new HashSet<>();
        
        System.out.println("Reading log file: " + logFilePath);
        
        final var lines = Files.readAllLines(logFilePath);
        int lineNumber = 0;
        
        for (final String line : lines) {
            lineNumber++;
            
            // Skip empty lines
            if (line.trim().isEmpty()) {
                continue;
            }
            
            // Extract SIM number from the line
            final String simNumber = extractSimNumber(line);
            if (simNumber == null) {
                System.out.println("Warning: No valid SIM number found in line " + lineNumber + ": " + line);
                continue;
            }
            
            // Check for state transitions
            if (ACTIVE_LIVE_TO_SUSPEND_PATTERN.matcher(line).find()) {
                suspendedSims.add(simNumber);
                System.out.println("Added SIM to suspended list: " + simNumber + " (line " + lineNumber + ")");
            } else if (ACTIVE_SUSPEND_TO_LIVE_PATTERN.matcher(line).find()) {
                final boolean removed = suspendedSims.remove(simNumber);
                if (removed) {
                    System.out.println("Removed SIM from suspended list: " + simNumber + " (line " + lineNumber + ")");
                } else {
                    System.out.println("Warning: Attempted to remove SIM that wasn't in suspended list: " + simNumber + " (line " + lineNumber + ")");
                }
            } else {
                System.out.println("Warning: No recognized state transition found in line " + lineNumber + ": " + line);
            }
        }
        
        System.out.println("Processed " + lineNumber + " lines");
        System.out.println("Final suspended SIMs count: " + suspendedSims.size());
        
        return suspendedSims;
    }
    
    /**
     * Extracts a 15-digit SIM number from a log line
     * 
     * @param line The log line to parse
     * @return The SIM number if found, null otherwise
     */
    private String extractSimNumber(final String line) {
        final Matcher matcher = SIM_PATTERN.matcher(line);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }
}
