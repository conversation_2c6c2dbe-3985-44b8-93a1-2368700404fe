package com.my_mueller.simlogparser;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;

@SpringBootApplication
@EnableConfigurationProperties
public class SimlogparserApplication {

	public static void main(String[] args) {
		// Disable web environment for command line application
		SpringApplication app = new SpringApplication(SimlogparserApplication.class);
		app.setWebApplicationType(org.springframework.boot.WebApplicationType.NONE);
		app.run(args);
	}

}
