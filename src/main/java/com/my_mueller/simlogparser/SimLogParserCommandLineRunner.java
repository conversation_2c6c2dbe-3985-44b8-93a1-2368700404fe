package com.my_mueller.simlogparser;

import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

@Component
public class SimLogParserCommandLineRunner implements CommandLineRunner {

    private final SimLogParserService parserService;
    private final SqlGeneratorService sqlGeneratorService;

    public SimLogParserCommandLineRunner(final SimLogParserService parserService, final SqlGeneratorService sqlGeneratorService) {
        this.parserService = parserService;
        this.sqlGeneratorService = sqlGeneratorService;
    }

    @Override
    public void run(final String... args) throws Exception {
        if (args.length != 2) {
            System.err.println("Usage: java -jar simlogparser.jar <sourcefile> <targetdir>");
            System.err.println("  sourcefile - the file to parse");
            System.err.println("  targetdir  - directory to place generated files");
            System.exit(1);
        }

        final String sourceFile = args[0];
        final String targetDir = args[1];

        // Validate source file exists
        final Path sourcePath = Paths.get(sourceFile);
        if (!Files.exists(sourcePath)) {
            System.err.println("Error: Source file does not exist: " + sourceFile);
            System.exit(1);
        }

        // Validate/create target directory
        final Path targetPath = Paths.get(targetDir);
        if (!Files.exists(targetPath)) {
            try {
                Files.createDirectories(targetPath);
                System.out.println("Created target directory: " + targetDir);
            } catch (final Exception e) {
                System.err.println("Error: Could not create target directory: " + targetDir);
                System.err.println(e.getMessage());
                System.exit(1);
            }
        } else if (!Files.isDirectory(targetPath)) {
            System.err.println("Error: Target path is not a directory: " + targetDir);
            System.exit(1);
        }

        System.out.println("Starting SIM log parsing...");
        System.out.println("Source file: " + sourceFile);
        System.out.println("Target directory: " + targetDir);

        try {
            // Parse the log file
            final var suspendedSims = parserService.parseLogFile(sourcePath);
            
            System.out.println("Found " + suspendedSims.size() + " SIMs in suspended state");
            
            // Generate SQL file
            sqlGeneratorService.generateSqlFile(suspendedSims, targetPath);
            
            System.out.println("Processing completed successfully!");
            
        } catch (final Exception e) {
            System.err.println("Error during processing: " + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
    }
}
