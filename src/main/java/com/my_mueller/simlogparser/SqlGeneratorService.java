package com.my_mueller.simlogparser;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Set;

@Service
public class SqlGeneratorService {

    @Value("${sql.update.statement:UPDATE sim_cards SET status = 'SUSPENDED', last_updated = CURRENT_TIMESTAMP WHERE sim_number = '{sim_number}';}")
    private String updateStatementTemplate;

    @Value("${sql.audit.statement:INSERT INTO sim_audit_log (sim_number, action, timestamp, reason) VALUES ('{sim_number}', 'SUSPEND', CURRENT_TIMESTAMP, 'Automated suspension from log parser');}")
    private String auditStatementTemplate;

    private static final DateTimeFormatter TIMESTAMP_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * Generates SQL file with statements for suspended SIMs
     * 
     * @param suspendedSims Set of SIM numbers that are suspended
     * @param targetDir Directory where to save the SQL file
     * @throws IOException if there's an error writing the file
     */
    public void generateSqlFile(final Set<String> suspendedSims, final Path targetDir) throws IOException {
        if (suspendedSims.isEmpty()) {
            System.out.println("No suspended SIMs found, skipping SQL file generation");
            return;
        }

        final String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        final Path sqlFilePath = targetDir.resolve("suspended_sims_" + timestamp + ".sql");
        
        final StringBuilder sqlContent = new StringBuilder();
        
        // Add header comment
        sqlContent.append("-- Generated SQL statements for suspended SIMs\n");
        sqlContent.append("-- Generated on: ").append(LocalDateTime.now().format(TIMESTAMP_FORMATTER)).append("\n");
        sqlContent.append("-- Total suspended SIMs: ").append(suspendedSims.size()).append("\n");
        sqlContent.append("-- \n");
        sqlContent.append("-- This file contains SQL statements for SIMs that are currently in suspended state\n");
        sqlContent.append("-- Modify the statements below according to your database schema and requirements\n");
        sqlContent.append("\n");
        
        // Add configuration section
        sqlContent.append("-- Configuration: Modify these statements according to your needs\n");
        sqlContent.append("-- Example statements - replace with your actual SQL operations\n");
        sqlContent.append("\n");
        
        // Generate SQL statements for each suspended SIM
        for (final String simNumber : suspendedSims) {
            sqlContent.append("-- Processing SIM: ").append(simNumber).append("\n");

            // Use configurable SQL statements
            final String updateStatement = updateStatementTemplate.replace("{sim_number}", simNumber);
            final String auditStatement = auditStatementTemplate.replace("{sim_number}", simNumber);

            sqlContent.append(updateStatement).append("\n");
            sqlContent.append(auditStatement).append("\n");
            sqlContent.append("\n");
        }
        
        // Add footer
        sqlContent.append("-- End of generated SQL statements\n");
        sqlContent.append("-- Total statements generated: ").append(suspendedSims.size() * 2).append("\n");
        
        // Write to file
        Files.write(sqlFilePath, sqlContent.toString().getBytes());
        
        System.out.println("SQL file generated: " + sqlFilePath);
        System.out.println("Generated " + (suspendedSims.size() * 2) + " SQL statements for " + suspendedSims.size() + " suspended SIMs");
        
        // Also generate a summary file
        generateSummaryFile(suspendedSims, targetDir, timestamp);
    }
    
    /**
     * Generates a summary file with the list of suspended SIMs
     * 
     * @param suspendedSims Set of suspended SIM numbers
     * @param targetDir Target directory
     * @param timestamp Timestamp for filename
     * @throws IOException if there's an error writing the file
     */
    private void generateSummaryFile(final Set<String> suspendedSims, final Path targetDir, final String timestamp) throws IOException {
        final Path summaryFilePath = targetDir.resolve("suspended_sims_summary_" + timestamp + ".txt");
        
        final StringBuilder summaryContent = new StringBuilder();
        summaryContent.append("Suspended SIMs Summary\n");
        summaryContent.append("Generated on: ").append(LocalDateTime.now().format(TIMESTAMP_FORMATTER)).append("\n");
        summaryContent.append("Total count: ").append(suspendedSims.size()).append("\n");
        summaryContent.append("\n");
        summaryContent.append("SIM Numbers:\n");
        summaryContent.append("=============\n");
        
        for (final String simNumber : suspendedSims.stream().sorted().toList()) {
            summaryContent.append(simNumber).append("\n");
        }
        
        Files.write(summaryFilePath, summaryContent.toString().getBytes());
        System.out.println("Summary file generated: " + summaryFilePath);
    }
}
