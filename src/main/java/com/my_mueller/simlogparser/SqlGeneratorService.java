package com.my_mueller.simlogparser;

import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Set;

@Service
public class SqlGeneratorService {

    private final SqlConfiguration sqlConfiguration;

    public SqlGeneratorService(SqlConfiguration sqlConfiguration) {
        this.sqlConfiguration = sqlConfiguration;
    }

    private static final DateTimeFormatter TIMESTAMP_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * Generates SQL file with statements for suspended SIMs
     * 
     * @param suspendedSims Set of SIM numbers that are suspended
     * @param targetDir Directory where to save the SQL file
     * @throws IOException if there's an error writing the file
     */
    public void generateSqlFile(final Set<String> suspendedSims, final Path targetDir) throws IOException {
        if (suspendedSims.isEmpty()) {
            System.out.println("No suspended SIMs found, skipping SQL file generation");
            return;
        }

        List<String> sqlStatements = sqlConfiguration.getStatements();
        if (sqlStatements.isEmpty()) {
            System.out.println("Warning: No SQL statements configured. Using default statements.");
            sqlStatements = getDefaultStatements();
        }

        final String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        final Path sqlFilePath = targetDir.resolve("suspended_sims_" + timestamp + ".sql");

        final StringBuilder sqlContent = new StringBuilder();
        
        // Add header comment
        sqlContent.append("-- Generated SQL statements for suspended SIMs\n");
        sqlContent.append("-- Generated on: ").append(LocalDateTime.now().format(TIMESTAMP_FORMATTER)).append("\n");
        sqlContent.append("-- Total suspended SIMs: ").append(suspendedSims.size()).append("\n");
        sqlContent.append("-- Total SQL statements per SIM: ").append(sqlStatements.size()).append("\n");
        sqlContent.append("-- \n");
        sqlContent.append("-- This file contains SQL statements for SIMs that are currently in suspended state\n");
        sqlContent.append("-- SQL statements are configured in application.properties\n");
        sqlContent.append("\n");

        // Add configuration section
        sqlContent.append("-- Configured SQL statements:\n");
        for (int i = 0; i < sqlStatements.size(); i++) {
            sqlContent.append("-- ").append(i + 1).append(". ").append(sqlStatements.get(i)).append("\n");
        }
        sqlContent.append("\n");
        
        // Generate SQL statements for each suspended SIM
        for (final String simNumber : suspendedSims) {
            sqlContent.append("-- Processing SIM: ").append(simNumber).append("\n");

            // Apply all configured SQL statements
            for (String statementTemplate : sqlStatements) {
                String statement = statementTemplate.replace("{sim_number}", simNumber);
                sqlContent.append(statement).append("\n");
            }
            sqlContent.append("\n");
        }
        
        // Add footer
        int totalStatements = suspendedSims.size() * sqlStatements.size();
        sqlContent.append("-- End of generated SQL statements\n");
        sqlContent.append("-- Total statements generated: ").append(totalStatements).append("\n");

        // Write to file
        Files.write(sqlFilePath, sqlContent.toString().getBytes());

        System.out.println("SQL file generated: " + sqlFilePath);
        System.out.println("Generated " + totalStatements + " SQL statements (" + sqlStatements.size() + " per SIM) for " + suspendedSims.size() + " suspended SIMs");
        
        // Also generate a summary file
        generateSummaryFile(suspendedSims, targetDir, timestamp);
    }
    
    /**
     * Generates a summary file with the list of suspended SIMs
     * 
     * @param suspendedSims Set of suspended SIM numbers
     * @param targetDir Target directory
     * @param timestamp Timestamp for filename
     * @throws IOException if there's an error writing the file
     */
    private void generateSummaryFile(final Set<String> suspendedSims, final Path targetDir, final String timestamp) throws IOException {
        final Path summaryFilePath = targetDir.resolve("suspended_sims_summary_" + timestamp + ".txt");
        
        final StringBuilder summaryContent = new StringBuilder();
        summaryContent.append("Suspended SIMs Summary\n");
        summaryContent.append("Generated on: ").append(LocalDateTime.now().format(TIMESTAMP_FORMATTER)).append("\n");
        summaryContent.append("Total count: ").append(suspendedSims.size()).append("\n");
        summaryContent.append("\n");
        summaryContent.append("SIM Numbers:\n");
        summaryContent.append("=============\n");
        
        for (final String simNumber : suspendedSims.stream().sorted().toList()) {
            summaryContent.append(simNumber).append("\n");
        }
        
        Files.write(summaryFilePath, summaryContent.toString().getBytes());
        System.out.println("Summary file generated: " + summaryFilePath);
    }

    /**
     * Returns default SQL statements when none are configured
     *
     * @return List of default SQL statement templates
     */
    private List<String> getDefaultStatements() {
        return List.of(
            "UPDATE sim_cards SET status = 'SUSPENDED', last_updated = CURRENT_TIMESTAMP WHERE sim_number = '{sim_number}';",
            "INSERT INTO sim_audit_log (sim_number, action, timestamp, reason) VALUES ('{sim_number}', 'SUSPEND', CURRENT_TIMESTAMP, 'Automated suspension from log parser');"
        );
    }
}
