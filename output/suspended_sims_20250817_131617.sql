-- Generated SQL statements for suspended SIMs
-- Generated on: 2025-08-17 13:16:17
-- Total suspended SIMs: 2
-- Total SQL statements per SIM: 3
-- 
-- This file contains SQL statements for SIMs that are currently in suspended state
-- SQL statements are configured in application.properties

-- Configured SQL statements:
-- 1. UPDATE sim_cards SET status = 'SUSPENDED', last_updated = CURRENT_TIMESTAMP WHERE sim_number = '{sim_number}';
-- 2. INSERT INTO sim_audit_log (sim_number, action, timestamp, reason) VALUES ('{sim_number}', 'SUSPEND', CURRENT_TIMESTAMP, 'Automated suspension from log parser');
-- 3. UPDATE sim_billing SET billing_status = 'SUSPENDED' WHERE sim_number = '{sim_number}';

-- Processing SIM: 123456789012344
UPDATE sim_cards SET status = 'SUSPENDED', last_updated = CURRENT_TIMESTAMP WHERE sim_number = '123456789012344';
INSERT INTO sim_audit_log (sim_number, action, timestamp, reason) VALUES ('123456789012344', 'SUSPEND', CURRENT_TIMESTAMP, 'Automated suspension from log parser');
UPDATE sim_billing SET billing_status = 'SUSPENDED' WHERE sim_number = '123456789012344';

-- Processing SIM: 123456789012345
UPDATE sim_cards SET status = 'SUSPENDED', last_updated = CURRENT_TIMESTAMP WHERE sim_number = '123456789012345';
INSERT INTO sim_audit_log (sim_number, action, timestamp, reason) VALUES ('123456789012345', 'SUSPEND', CURRENT_TIMESTAMP, 'Automated suspension from log parser');
UPDATE sim_billing SET billing_status = 'SUSPENDED' WHERE sim_number = '123456789012345';

-- End of generated SQL statements
-- Total statements generated: 6
